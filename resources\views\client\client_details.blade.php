@extends('layouts.layout')
@section('content')
    <div class="container-fluid flex-grow-1 container-p-y">
        <!-- Enhanced Header Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                    <div class="d-flex align-items-center gap-3">
                        <a href="#" class="btn btn-outline-primary waves-effect">
                            <i class="mdi mdi-arrow-left me-1"></i> Back
                        </a>
                        <div>
                            <h4 class="mb-1 fw-bold text-primary">
                                <i class="mdi mdi-solar-power me-2"></i>
                                {{ $client[0]['customer_name'] ?? 'Solar Client' }}
                            </h4>
                            <p class="text-muted mb-0">
                                <i class="mdi mdi-identifier me-1"></i>
                                Customer ID: <span class="fw-medium">{{ $client[0]['customer_number'] ?? 'N/A' }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-secondary waves-effect">
                            <i class="mdi mdi-printer me-1"></i> Print
                        </button>
                        <button class="btn btn-primary waves-effect">
                            <i class="mdi mdi-pencil me-1"></i> Edit
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Enhanced Sidebar -->
            <div class="col-xxl-4 col-xl-4 col-lg-5">
                <!-- Client Overview Card -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="mb-0 text-white">
                            <i class="mdi mdi-account-circle me-2"></i>
                            Client Overview
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <!-- Client Avatar Section -->
                        <div class="text-center py-4 bg-light">
                            <div class="avatar avatar-xl mb-3">
                                <div class="avatar-initial rounded-circle bg-primary text-white fs-2 fw-bold">
                                    {{ substr($client[0]['customer_name'] ?? 'N', 0, 1) }}
                                </div>
                            </div>
                            <h5 class="mb-1">{{ $client[0]['customer_name'] ?? 'N/A' }}</h5>
                            <p class="text-muted mb-2">
                                <i class="mdi mdi-phone me-1"></i>
                                {{ $client[0]['mobile'] ?? 'N/A' }}
                            </p>
                            <span class="badge bg-{{ $client[0]['status'] == 'Agreed' ? 'success' : 'warning' }} px-3 py-2">
                                <i class="mdi mdi-check-circle me-1"></i>
                                {{ $client[0]['status'] ?? 'N/A' }}
                            </span>
                        </div>

                        <!-- Key Information -->
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between align-items-center py-3">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-3">
                                        <div class="avatar-initial rounded bg-label-success">
                                            <i class="mdi mdi-currency-inr"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Total Amount</h6>
                                        <small class="text-muted">Solar Installation</small>
                                    </div>
                                </div>
                                <span
                                    class="fw-bold text-success fs-5">₹{{ number_format($client[0]['solar_total_amount'] ?? 0, 2) }}</span>
                            </div>

                            <div class="list-group-item d-flex justify-content-between align-items-center py-3">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-3">
                                        <div class="avatar-initial rounded bg-label-warning">
                                            <i class="mdi mdi-solar-power"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">System Capacity</h6>
                                        <small class="text-muted">Solar Power</small>
                                    </div>
                                </div>
                                <span class="badge bg-label-warning fs-6">{{ $client[0]['capacity'] ?? 'N/A' }}</span>
                            </div>

                            <div class="list-group-item d-flex justify-content-between align-items-center py-3">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-3">
                                        <div class="avatar-initial rounded bg-label-info">
                                            <i class="mdi mdi-calendar"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Registration Date</h6>
                                        <small class="text-muted">Installation Date</small>
                                    </div>
                                </div>
                                <span
                                    class="text-muted">{{ date('d M Y', strtotime($client[0]['registration_date'] ?? '')) }}</span>
                            </div>

                            <div class="list-group-item d-flex justify-content-between align-items-center py-3">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-3">
                                        <div class="avatar-initial rounded bg-label-primary">
                                            <i class="mdi mdi-account-hard-hat"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Installer</h6>
                                        <small class="text-muted">Assigned Technician</small>
                                    </div>
                                </div>
                                <span class="text-dark fw-medium">{{ $client[0]['installer_name'] ?? 'N/A' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-xxl-8 col-xl-8 col-lg-7">
                <!-- System Specifications Card -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-gradient-info text-white">
                        <h5 class="mb-0 text-white">
                            <i class="mdi mdi-solar-panel me-2"></i>
                            System Specifications
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- Solar Capacity -->
                            <div class="col-md-6 col-lg-4">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-warning">
                                            <i class="mdi mdi-solar-power text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Solar Capacity</h6>
                                        <span class="badge bg-warning">{{ $client[0]['capacity'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Roof Type -->
                            <div class="col-md-6 col-lg-4">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-info">
                                            <i class="mdi mdi-home-roof text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Roof Type</h6>
                                        <span class="text-dark fw-medium">{{ $client[0]['roof_type'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Roof Area -->
                            <div class="col-md-6 col-lg-4">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-primary">
                                            <i class="mdi mdi-view-grid-outline text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Roof Area</h6>
                                        <span class="badge bg-primary">{{ $client[0]['roof_area'] ?? 'N/A' }} sq ft</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Solar Company -->
                            <div class="col-md-6 col-lg-4">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-success">
                                            <i class="mdi mdi-flash text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Solar Company</h6>
                                        <span
                                            class="text-dark fw-medium">{{ $client[0]['solar_company'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Inverter Company -->
                            <div class="col-md-6 col-lg-4">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-info">
                                            <i class="mdi mdi-lightning-bolt text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Inverter Company</h6>
                                        <span
                                            class="text-dark fw-medium">{{ $client[0]['inverter_company'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Usage Pattern -->
                            <div class="col-md-6 col-lg-4">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-secondary">
                                            <i class="mdi mdi-factory text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Usage Pattern</h6>
                                        <span class="badge bg-secondary">{{ $client[0]['usage_pattern'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Information Card -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-gradient-success text-white">
                        <h5 class="mb-0 text-white">
                            <i class="mdi mdi-currency-inr me-2"></i>
                            Financial Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- Loan Required -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-primary">
                                            <i class="mdi mdi-bank text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Loan Required</h6>
                                        <span
                                            class="badge bg-{{ $client[0]['loan_required'] == 'Yes' ? 'success' : 'secondary' }}">
                                            {{ $client[0]['loan_required'] ?? 'N/A' }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Loan Status -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-warning">
                                            <i class="mdi mdi-cash text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Loan Status</h6>
                                        <span
                                            class="badge bg-{{ $client[0]['loan_status'] == 'Disbursed' ? 'success' : 'warning' }}">
                                            {{ $client[0]['loan_status'] ?? 'N/A' }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Subsidy Status -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-info">
                                            <i class="mdi mdi-gift text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Subsidy Status</h6>
                                        <span
                                            class="badge bg-{{ $client[0]['subsidy_status'] == 'Approved' ? 'success' : 'warning' }}">
                                            {{ $client[0]['subsidy_status'] ?? 'N/A' }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Channel Partner -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-success">
                                            <i class="mdi mdi-account-tie text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Channel Partner</h6>
                                        <span
                                            class="text-dark fw-medium">{{ $client[0]['channel_partner_name'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Address Information Card -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="mb-0 text-white">
                            <i class="mdi mdi-map-marker me-2"></i>
                            Address Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- Customer Address -->
                            <div class="col-12">
                                <div class="d-flex align-items-start p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-info">
                                            <i class="mdi mdi-home-map-marker text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">Customer Address</h6>
                                        <p class="text-muted mb-0">{{ $client[0]['customer_address'] ?? 'N/A' }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Residential Address -->
                            <div class="col-12">
                                <div class="d-flex align-items-start p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-success">
                                            <i class="mdi mdi-home-city text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">Residential Address</h6>
                                        <p class="text-muted mb-0">
                                            {{ $client[0]['customer_residential_address'] ?? 'N/A' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Card -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-gradient-secondary text-white">
                        <h5 class="mb-0 text-white">
                            <i class="mdi mdi-information-outline me-2"></i>
                            Additional Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- Age -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-info">
                                            <i class="mdi mdi-account-circle text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Age</h6>
                                        <span class="badge bg-info">{{ $client[0]['age'] ?? 'N/A' }} years</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Consumer Number -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-warning">
                                            <i class="mdi mdi-identifier text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Consumer Number</h6>
                                        <span class="text-dark fw-medium">{{ $client[0]['consumer_no'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Mode -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-success">
                                            <i class="mdi mdi-credit-card text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Payment Mode</h6>
                                        <span
                                            class="badge bg-{{ $client[0]['payment_mode'] == 'cash' ? 'success' : 'primary' }}">
                                            {{ ucfirst($client[0]['payment_mode'] ?? 'N/A') }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Jan Samarth ID -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-primary">
                                            <i class="mdi mdi-card-account-details text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Jan Samarth ID</h6>
                                        <span
                                            class="text-dark fw-medium">{{ $client[0]['jan_samarth_id'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Acknowledge Number -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-success">
                                            <i class="mdi mdi-check-circle text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Acknowledge Number</h6>
                                        <span
                                            class="text-dark fw-medium">{{ $client[0]['acknowledge_no'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Application Ref Number -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 bg-light rounded-3">
                                    <div class="avatar avatar-md me-3">
                                        <div class="avatar-initial rounded bg-info">
                                            <i class="mdi mdi-file-document text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Application Ref Number</h6>
                                        <span
                                            class="text-dark fw-medium">{{ $client[0]['application_ref_no'] ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Documents Section -->
                <div class="card shadow-sm">
                    <div class="card-header bg-gradient-dark text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 text-white">
                                <i class="mdi mdi-file-document-multiple me-2"></i>
                                Installation Documents
                            </h5>
                            <div class="d-flex align-items-center gap-2">
                                <button class="btn btn-outline-light btn-sm waves-effect">
                                    <i class="mdi mdi-plus me-1"></i>Add Document
                                </button>
                                <button class="btn btn-outline-light btn-sm" id="toggle-documents">
                                    <i class="mdi mdi-chevron-up" id="toggle-icon"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body" id="documents-section">
                        @if ($client[0]['cancel_cheque'] || $client[0]['light_bill'])
                            <div class="row g-4">
                                @if ($client[0]['cancel_cheque'])
                                    <div class="col-lg-6">
                                        <div class="document-card p-4 border rounded-3 bg-light h-100">
                                            <div class="d-flex align-items-center justify-content-between mb-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-lg me-3">
                                                        <div class="avatar-initial rounded bg-info">
                                                            <i class="mdi mdi-file-document text-white fs-4"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1 fw-bold">Cancelled Cheque</h6>
                                                        <small class="text-muted">Banking Document</small>
                                                    </div>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                        type="button" data-bs-toggle="dropdown">
                                                        <i class="mdi mdi-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item"
                                                                href="{{ asset('storage/' . $client[0]['cancel_cheque']) }}"
                                                                target="_blank">
                                                                <i class="mdi mdi-eye me-2"></i>View
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item"
                                                                href="{{ asset('storage/' . $client[0]['cancel_cheque']) }}"
                                                                download>
                                                                <i class="mdi mdi-download me-2"></i>Download
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge bg-info">PDF Document</span>
                                                <small class="text-muted">
                                                    <i class="mdi mdi-calendar me-1"></i>
                                                    {{ date('d M Y', strtotime($client[0]['created_at'])) }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                @if ($client[0]['light_bill'])
                                    <div class="col-lg-6">
                                        <div class="document-card p-4 border rounded-3 bg-light h-100">
                                            <div class="d-flex align-items-center justify-content-between mb-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-lg me-3">
                                                        <div class="avatar-initial rounded bg-warning">
                                                            <i class="mdi mdi-file-document text-white fs-4"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1 fw-bold">Electricity Bill</h6>
                                                        <small class="text-muted">Utility Document</small>
                                                    </div>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                        type="button" data-bs-toggle="dropdown">
                                                        <i class="mdi mdi-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item"
                                                                href="{{ asset('storage/' . $client[0]['light_bill']) }}"
                                                                target="_blank">
                                                                <i class="mdi mdi-eye me-2"></i>View
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item"
                                                                href="{{ asset('storage/' . $client[0]['light_bill']) }}"
                                                                download>
                                                                <i class="mdi mdi-download me-2"></i>Download
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge bg-warning">PDF Document</span>
                                                <small class="text-muted">
                                                    <i class="mdi mdi-calendar me-1"></i>
                                                    {{ date('d M Y', strtotime($client[0]['created_at'])) }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @else
                            <div class="text-center py-5">
                                <div class="avatar avatar-xl mb-3">
                                    <div class="avatar-initial rounded-circle bg-label-secondary">
                                        <i class="mdi mdi-file-document-outline fs-2"></i>
                                    </div>
                                </div>
                                <h6 class="mb-2">No Documents Available</h6>
                                <p class="text-muted mb-4">Upload documents related to this solar installation</p>
                                <button class="btn btn-primary waves-effect">
                                    <i class="mdi mdi-plus me-1"></i>Upload Document
                                </button>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Styles -->
    <style>
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .bg-gradient-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .bg-gradient-warning {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
        }

        .bg-gradient-secondary {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
        }

        .bg-gradient-dark {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
        }

        .document-card {
            transition: all 0.3s ease;
            border: 2px solid transparent !important;
        }

        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
            border-color: #667eea !important;
        }

        .avatar-xl {
            width: 4rem;
            height: 4rem;
        }

        .avatar-lg {
            width: 3rem;
            height: 3rem;
        }

        .avatar-md {
            width: 2.5rem;
            height: 2.5rem;
        }

        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .list-group-item {
            border: none;
            border-bottom: 1px solid #f1f1f1;
        }

        .list-group-item:last-child {
            border-bottom: none;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .text-primary {
            color: #667eea !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
        }

        .btn-outline-primary:hover {
            background-color: #667eea;
            border-color: #667eea;
        }

        @media (max-width: 768px) {

            .col-xxl-4,
            .col-xl-4,
            .col-lg-5 {
                margin-bottom: 2rem;
            }
        }
    </style>

    <!-- Enhanced JavaScript -->
    <script>
        $(document).ready(function() {
            // Documents section toggle
            $('#toggle-documents').click(function() {
                const section = $('#documents-section');
                const icon = $('#toggle-icon');

                if (section.is(':visible')) {
                    section.slideUp(300);
                    icon.removeClass('mdi-chevron-up').addClass('mdi-chevron-down');
                } else {
                    section.slideDown(300);
                    icon.removeClass('mdi-chevron-down').addClass('mdi-chevron-up');
                }
            });

            // Add smooth scrolling for better UX
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });

            // Add loading states for buttons
            $('.btn').on('click', function() {
                const btn = $(this);
                if (!btn.hasClass('no-loading')) {
                    btn.addClass('disabled');
                    setTimeout(() => {
                        btn.removeClass('disabled');
                    }, 1000);
                }
            });

            // Initialize tooltips if Bootstrap tooltips are available
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        });
    </script>
@endsection
